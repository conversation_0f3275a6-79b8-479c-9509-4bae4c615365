export interface Auth {
  preferred_username: string;
  picture: string;
  name: string;
  isLocalUse?: boolean;
}

export interface HtmlHistory {
  html: string;
  createdAt: Date;
  prompt: string;
}

export interface SavedFile {
  id: string;
  name: string;
  html: string;
  createdAt: Date;
  modifiedAt: Date;
  history: HtmlHistory[];
}

export interface FileManagerState {
  files: SavedFile[];
  currentFileId: string | null;
}
