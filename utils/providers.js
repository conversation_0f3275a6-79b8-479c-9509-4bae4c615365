export const PROVIDERS = {
  "fireworks-ai": {
    name: "Fireworks AI",
    max_tokens: 131_000,
    id: "fireworks-ai",
  },
  nebius: {
    name: "Nebius AI Studio",
    max_tokens: 131_000,
    id: "nebius",
  },
  sambanova: {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    max_tokens: 32_000,
    id: "sambanova",
  },
  novita: {
    name: "NovitaAI",
    max_tokens: 16_000,
    id: "novita",
  },
  hyperbolic: {
    name: "Hyperbolic",
    max_tokens: 131_000,
    id: "hyperbolic",
  },
  together: {
    name: "Together AI",
    max_tokens: 128_000,
    id: "together",
  },
  google: {
    name: "Google AI",
    max_tokens: 1_000_000,
    id: "google",
  },
  openrouter: {
    name: "OpenRouter",
    max_tokens: 200_000,
    id: "openrouter",
    requiresApiKey: true,
    supportsCustomModels: true,
  },
};

export const MODELS = [
  {
    value: "deepseek-ai/DeepSeek-V3-0324",
    label: "DeepSeek V3 O324",
    providers: ["fireworks-ai", "nebius", "sambanova", "novita", "hyperbolic"],
    autoProvider: "fireworks-ai",
  },
  {
    value: "deepseek-ai/DeepSeek-R1-0528",
    label: "DeepSeek R1 0528",
    providers: [
      "fireworks-ai",
      "novita",
      "hyperbolic",
      "nebius",
      "together",
      "sambanova",
    ],
    autoProvider: "novita",
    isNew: true,
    isThinker: true,
  },
  {
    value: "gemini-1.5-pro",
    label: "Gemini 1.5 Pro",
    providers: ["google"],
    autoProvider: "google",
  },
  {
    value: "gemini-1.5-flash",
    label: "Gemini 1.5 Flash",
    providers: ["google"],
    autoProvider: "google",
  },
  {
    value: "gemini-2.5-pro-preview-06-05",
    label: "Gemini 2.5 Pro Preview",
    providers: ["google"],
    autoProvider: "google",
    isNew: true,
  },
  {
    value: "gemini-2.5-pro-preview-05-06",
    label: "Gemini 2.5 Pro Preview 05-06",
    providers: ["google"],
    autoProvider: "google",
  },
  {
    value: "gemini-2.5-flash-preview-04-17",
    label: "Gemini 2.5 Flash Preview 04-17",
    providers: ["google"],
    autoProvider: "google",
  },
  {
    value: "gemini-2.5-flash-preview-05-20",
    label: "Gemini 2.5 Flash Preview 05-20",
    providers: ["google"],
    autoProvider: "google",
    isNew: true,
  },
  {
    value: "anthropic/claude-3.5-sonnet",
    label: "Claude 3.5 Sonnet",
    providers: ["openrouter"],
    autoProvider: "openrouter",
  },
  {
    value: "openai/gpt-4o",
    label: "GPT-4o",
    providers: ["openrouter"],
    autoProvider: "openrouter",
  },
  {
    value: "openai/gpt-4o-mini",
    label: "GPT-4o Mini",
    providers: ["openrouter"],
    autoProvider: "openrouter",
  },
  {
    value: "meta-llama/llama-3.1-405b-instruct",
    label: "Llama 3.1 405B Instruct",
    providers: ["openrouter"],
    autoProvider: "openrouter",
  },
  {
    value: "custom",
    label: "Custom Model",
    providers: ["openrouter"],
    autoProvider: "openrouter",
    isCustom: true,
  },
];
