import { useState, useRef } from "react";
import { toast } from "sonner";
import classNames from "classnames";
import { 
  FileText, 
  Plus, 
  MoreVertical, 
  Edit3, 
  Trash2, 
  Download,
  Upload,
  Search,
  Calendar,
  Clock
} from "lucide-react";

import { SavedFile } from "../../../utils/types";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";

interface FileBrowserProps {
  files: SavedFile[];
  currentFileId: string | null;
  onFileSelect: (fileId: string) => void;
  onFileCreate: (name: string) => void;
  onFileRename: (fileId: string, newName: string) => void;
  onFileDelete: (fileId: string) => void;
  onFileExport: (fileId: string) => void;
  onFileImport: (file: File) => void;
}

export default function FileBrowser({
  files,
  currentFileId,
  onFileSelect,
  onFileCreate,
  onFileRename,
  onFileDelete,
  onFileExport,
  onFileImport,
}: FileBrowserProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newFileName, setNewFileName] = useState("");
  const [editingFileId, setEditingFileId] = useState<string | null>(null);
  const [editingFileName, setEditingFileName] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const filteredFiles = files.filter(file =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCreateFile = () => {
    if (!newFileName.trim()) {
      toast.error("Please enter a file name");
      return;
    }
    
    const fileName = newFileName.trim().endsWith('.html') 
      ? newFileName.trim() 
      : `${newFileName.trim()}.html`;
    
    onFileCreate(fileName);
    setNewFileName("");
    setShowCreateForm(false);
    toast.success(`File "${fileName}" created successfully`);
  };

  const handleRenameFile = (fileId: string) => {
    if (!editingFileName.trim()) {
      toast.error("Please enter a file name");
      return;
    }
    
    const fileName = editingFileName.trim().endsWith('.html') 
      ? editingFileName.trim() 
      : `${editingFileName.trim()}.html`;
    
    onFileRename(fileId, fileName);
    setEditingFileId(null);
    setEditingFileName("");
    toast.success("File renamed successfully");
  };

  const handleDeleteFile = (fileId: string, fileName: string) => {
    if (window.confirm(`Are you sure you want to delete "${fileName}"?`)) {
      onFileDelete(fileId);
      toast.success(`File "${fileName}" deleted successfully`);
    }
  };

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onFileImport(file);
      event.target.value = ""; // Reset input
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  return (
    <div className="h-full bg-neutral-950 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-neutral-800">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-white">Files</h2>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => fileInputRef.current?.click()}
              className="text-neutral-400 hover:text-white"
            >
              <Upload className="size-4" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowCreateForm(true)}
              className="text-neutral-400 hover:text-white"
            >
              <Plus className="size-4" />
            </Button>
          </div>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-neutral-500" />
          <Input
            placeholder="Search files..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-neutral-900 border-neutral-700 text-white placeholder-neutral-500"
          />
        </div>
      </div>

      {/* Create File Form */}
      {showCreateForm && (
        <div className="p-4 border-b border-neutral-800 bg-neutral-900">
          <div className="space-y-3">
            <Input
              placeholder="Enter file name..."
              value={newFileName}
              onChange={(e) => setNewFileName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleCreateFile();
                if (e.key === 'Escape') {
                  setShowCreateForm(false);
                  setNewFileName("");
                }
              }}
              className="bg-neutral-800 border-neutral-600 text-white"
              autoFocus
            />
            <div className="flex items-center gap-2">
              <Button size="sm" onClick={handleCreateFile}>
                Create
              </Button>
              <Button 
                size="sm" 
                variant="ghost" 
                onClick={() => {
                  setShowCreateForm(false);
                  setNewFileName("");
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* File List */}
      <div className="flex-1 overflow-y-auto">
        {filteredFiles.length === 0 ? (
          <div className="p-8 text-center text-neutral-500">
            <FileText className="size-12 mx-auto mb-3 opacity-50" />
            <p className="text-sm">
              {searchQuery ? "No files match your search" : "No files yet"}
            </p>
            {!searchQuery && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowCreateForm(true)}
                className="mt-2 text-neutral-400 hover:text-white"
              >
                <Plus className="size-4 mr-2" />
                Create your first file
              </Button>
            )}
          </div>
        ) : (
          <div className="p-2">
            {filteredFiles.map((file) => (
              <div
                key={file.id}
                className={classNames(
                  "group flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors",
                  {
                    "bg-neutral-800 border border-neutral-700": file.id === currentFileId,
                    "hover:bg-neutral-900": file.id !== currentFileId,
                  }
                )}
                onClick={() => onFileSelect(file.id)}
              >
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <FileText className="size-4 text-neutral-400 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    {editingFileId === file.id ? (
                      <Input
                        value={editingFileName}
                        onChange={(e) => setEditingFileName(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') handleRenameFile(file.id);
                          if (e.key === 'Escape') {
                            setEditingFileId(null);
                            setEditingFileName("");
                          }
                        }}
                        onBlur={() => handleRenameFile(file.id)}
                        className="bg-neutral-800 border-neutral-600 text-white text-sm h-6 px-2"
                        autoFocus
                        onClick={(e) => e.stopPropagation()}
                      />
                    ) : (
                      <>
                        <p className="text-white text-sm font-medium truncate">
                          {file.name}
                        </p>
                        <div className="flex items-center gap-3 text-xs text-neutral-500 mt-1">
                          <span className="flex items-center gap-1">
                            <Calendar className="size-3" />
                            {formatDate(file.createdAt)}
                          </span>
                          {file.modifiedAt !== file.createdAt && (
                            <span className="flex items-center gap-1">
                              <Clock className="size-3" />
                              {formatDate(file.modifiedAt)}
                            </span>
                          )}
                        </div>
                      </>
                    )}
                  </div>
                </div>
                
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="opacity-0 group-hover:opacity-100 text-neutral-400 hover:text-white"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MoreVertical className="size-4" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-48 p-1 bg-neutral-900 border-neutral-700">
                    <Button
                      size="sm"
                      variant="ghost"
                      className="w-full justify-start text-neutral-300 hover:text-white"
                      onClick={(e) => {
                        e.stopPropagation();
                        setEditingFileId(file.id);
                        setEditingFileName(file.name.replace('.html', ''));
                      }}
                    >
                      <Edit3 className="size-4 mr-2" />
                      Rename
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="w-full justify-start text-neutral-300 hover:text-white"
                      onClick={(e) => {
                        e.stopPropagation();
                        onFileExport(file.id);
                      }}
                    >
                      <Download className="size-4 mr-2" />
                      Export
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="w-full justify-start text-red-400 hover:text-red-300"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteFile(file.id, file.name);
                      }}
                    >
                      <Trash2 className="size-4 mr-2" />
                      Delete
                    </Button>
                  </PopoverContent>
                </Popover>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Hidden file input for import */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".html,.htm"
        onChange={handleFileImport}
        className="hidden"
      />
    </div>
  );
}
