<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" type="image/svg+xml" href="/logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>DeepSite | Build with AI ✨</title>
    <meta
      name="description"
      content="DeepSite is a web development tool that
    helps you build websites with AI, no code required. Let's deploy your
    website with DeepSite and enjoy the magic of AI."
    />
    <meta name="theme-color" content="#000000" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-title" content="DeepSite" />
    <meta name="msapplication-TileColor" content="#000000" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="DeepSite | Build with AI ✨" />
    <meta
      name="twitter:description"
      content="DeepSite is a web development tool that
    helps you build websites with AI, no code required. Let's deploy your
    website with DeepSite and enjoy the magic of AI."
    />
    <meta name="twitter:image" content="/banner.png" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="DeepSite | Build with AI ✨" />
    <meta
      property="og:description"
      content="DeepSite is a web development tool that
    helps you build websites with AI, no code required. Let's deploy your
    website with DeepSite and enjoy the magic of AI."
    />
    <meta property="og:image" content="/banner.png" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,100..900;1,100..900&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Source+Code+Pro:ital,wght@0,200..900;1,200..900&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
